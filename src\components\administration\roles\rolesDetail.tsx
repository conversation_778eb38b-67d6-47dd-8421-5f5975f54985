'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { RolesRow } from './roles'

interface RolesDetailProps {
  id: string
}



export default function RolesDetail({ id }: Readonly<RolesDetailProps>) {
  const router = useRouter()
  const [role, setRole] = useState<RolesRow | null>(null)

  useEffect(() => {
    // TODO: Replace with actual API call
    const mockRole: RolesRow = {
      id,
      name: 'Sample Role',
      description: 'A sample role for demonstration',
      isSystemAuthority: false,
      createdAt: '2023-10-15T14:30:00Z',
      updatedAt: '2023-10-15T14:30:00Z',
      permissions: [
        {
          resourceName: 'users',
          permission: 1
        },
        {
          resourceName: 'roles',
          permission: 2
        }
      ]
    }
    setRole(mockRole)
  }, [id])

  const handleBack = () => {
    router.back()
  }

  if (!role) {
    return <div>Loading...</div>
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <Card className="border rounded-md shadow-sm">
        <CardHeader className="flex items-center justify-between border-b p-4">
          <Button variant="ghost" size="sm" className="gap-1" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        </CardHeader>
        <CardContent className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <DetailBlock label="Name">{role.name}</DetailBlock>
              <DetailBlock label="Description">{role.description || 'No description'}</DetailBlock>
            </div>
            <div className="space-y-4">
              <DetailBlock label="System Authority">
                <Badge variant="outline" className={role.isSystemAuthority ? 'bg-blue-100 text-blue-600 border-none' : 'bg-gray-100 text-gray-600 border-none'}>
                  {role.isSystemAuthority ? 'Yes' : 'No'}
                </Badge>
              </DetailBlock>
              <DetailBlock label="Created At">
                {new Date(role.createdAt).toLocaleString()}
              </DetailBlock>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Block hiển thị label trên, value dưới, có border-b
function DetailBlock({ label, children }: Readonly<{ label: string; children?: React.ReactNode }>) {
  return (
    <div>
      <p className="text-sm text-muted-foreground mb-1">{label}</p>
      <div className="text-base font-medium pb-1 border-b">{children}</div>
    </div>
  )
}
